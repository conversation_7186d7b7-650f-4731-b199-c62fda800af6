import { z as zod } from 'zod';

import { PRODUCT_TYPES } from 'src/actions/mooly-chatbot/product-constants';

import { schemaHelper } from 'src/components/hook-form';

// ----------------------------------------------------------------------

// Schema đơn giản cho sản phẩm - chỉ các field cần thiết
const createSimpleProductSchema = () => zod.object({
    // Thông tin cơ bản - bắt buộc
    name: zod.string().min(1, 'Tên sản phẩm là bắt buộc'),
    description: zod.string().optional(),

    // Phân loại - bắt buộc
    categoryId: zod.string().min(1, '<PERSON>h mục là bắt buộc'),
    type: zod.enum([
      PRODUCT_TYPES.SIMPLE,
      PRODUCT_TYPES.VARIABLE,
      PRODUCT_TYPES.DIGITAL,
      PRODUCT_TYPES.SERVICE,
    ]).default(PRODUCT_TYPES.SIMPLE),

    // Giá - bắt buộc
    price: zod.number({
      required_error: 'Giá sản phẩm là bắt buộc',
      invalid_type_error: 'Giá phải là số'
    }).min(0, 'Giá phải lớn hơn hoặc bằng 0'),

    // Hình ảnh - bắt buộc
    images: zod.array(zod.any()).min(1, 'Ít nhất một hình ảnh sản phẩm là bắt buộc'),
    avatar: zod.any().optional(),

    // Trạng thái
    isActive: zod.boolean().default(true),

    // Các field tự động tạo hoặc có giá trị mặc định
    slug: zod.string().optional(),
    sku: zod.string().optional(), // Sẽ tự động tạo từ tên sản phẩm
    stockQuantity: zod.number().default(0),
    trackInventory: zod.boolean().default(false),

    // Các field tùy chọn khác
    costPrice: zod.number().min(0).optional().nullable(),
    salePrice: zod.number().min(0).optional().nullable(),
    weight: zod.number().min(0).optional().nullable(),
    length: zod.number().min(0).optional().nullable(),
    width: zod.number().min(0).optional().nullable(),
    height: zod.number().min(0).optional().nullable(),
    attributes: zod.any().default({}),
    variants: zod.any().default([]),
    tags: zod.array(zod.string()).default([]),
    gender: zod.array(zod.string()).default([]),
    saleLabel: zod.any().default({ enabled: false, content: '' }),
    newLabel: zod.any().default({ enabled: false, content: '' }),
    taxes: zod.number().min(0).optional().nullable(),
    includeTaxes: zod.boolean().default(false),
    metaKeywords: zod.array(zod.string()).default([]),
    seoTitle: zod.string().optional(),
    seoDescription: zod.string().optional(),
    isFeatured: zod.boolean().default(false),
    marketingInfo: zod.any().default({}),
    inventorySettings: zod.any().default({}),
    pricingSettings: zod.any().default({}),
    digitalProductInfo: zod.any().default({}),
    serviceInfo: zod.any().default({}),
    bundleInfo: zod.any().default({}),
    dimensions: zod.any().default({}),
  });

// ----------------------------------------------------------------------

// Schema chính - sử dụng schema đơn giản
export const ProductSchema = createSimpleProductSchema();

// Schema đơn giản cho sản phẩm simple
export const SimpleProductSchema = createSimpleProductSchema();

// Function để tạo schema dựa trên loại sản phẩm
export const getSchemaByType = (productType) => createSimpleProductSchema();

// ----------------------------------------------------------------------

// Default values cho sản phẩm đơn giản
export const simpleProductDefaultValues = {
  // Thông tin cơ bản
  name: '',
  description: '',

  // Phân loại
  categoryId: '',
  type: PRODUCT_TYPES.SIMPLE,

  // Giá
  price: null,

  // Hình ảnh và media
  images: [],
  avatar: null,

  // Trạng thái
  isActive: true,

  // Các field tự động tạo
  slug: '',
  sku: '',
  stockQuantity: 0,
  trackInventory: false,
};

// Default values - sử dụng giá trị đơn giản
export const defaultValues = simpleProductDefaultValues;
